// AI Video Generator Components
export { AIVideoGenerator } from './ai-video-generator';
export { VideoUploadInterface } from './video-upload-interface';
export { AIPromptInput } from './ai-prompt-input';
export { VideoPreviewPlayer } from './video-preview-player';
export { ProgressTracking } from './progress-tracking';
export { SettingsPanel } from './settings-panel';
export { ExportInterface } from './export-interface';
export { VideoEditingControls } from './video-editing-controls';
export { ThumbnailGenerator } from './thumbnail-generator';

// Enhanced Modular Components
export { EnhancedAIVideoGenerator } from './enhanced-ai-video-generator';
export { VideoEditorSidebar } from './video-editor-sidebar';
export { VideoPreviewPanel } from './video-preview-panel';
export { AIGenerationPanel } from './ai-generation-panel';
export { VideoTimeline } from './video-timeline';

// Re-export types for convenience
export type {
  AIVideoGeneratorState,
  UploadedFile,
  AIPromptData,
  AIVideoStyle,
  AIGenerationSettings,
  GeneratedVideo,
  ProcessingQueueItem,
  ExportSettings,
  VideoEditingState,
  VideoFilter,
  VideoTransformation,
  ThumbnailData
} from '@/lib/video/types/video-types';
