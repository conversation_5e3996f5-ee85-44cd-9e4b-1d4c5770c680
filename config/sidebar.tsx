import React from 'react';
import { HomeIcon, LibraryIcon, UsersIcon, BarChartIcon, SettingsIcon, BotIcon, SparklesIcon, PieChartIcon, TrendingUpIcon, VideoIcon, CameraIcon } from 'lucide-react';
import { SidebarSection } from '@/types/sidebar';

export const sidebarConfig: SidebarSection[] = [
  {
    id: 'main',
    title: 'Main',
    items: [
      {
        id: 'dashboard',
        title: 'Dashboard',
        icon: <HomeIcon className="h-4 w-4" />,
        href: '/dashboard',
      },
      {
        id: 'content-library',
        title: 'Content Library',
        icon: <LibraryIcon className="h-4 w-4" />,
        href: '/dashboard/content-hub',
      },
      {
        id: 'collaborations',
        title: 'Collaborations',
        icon: <UsersIcon className="h-4 w-4" />,
        href: '/dashboard/collaborators',
      },
    ],
  },
  {
    id: 'marketing',
    title: 'Marketing & AI',
    items: [
      {
        id: 'marketing',
        title: 'Marketing Hub',
        icon: <SparklesIcon className="h-4 w-4" />,
        href: '/dashboard/marketing',
      },
      {
        id: 'ai-assistant',
        title: 'AI Assistant',
        icon: <BotIcon className="h-4 w-4" />,
        href: '/dashboard/ai-assistant',
      },
      {
        id: 'ai-insights',
        title: 'AI Insights',
        icon: <TrendingUpIcon className="h-4 w-4" />,
        href: '/dashboard/ai-insights',
      },
    ],
  },
  {
    id: 'video',
    title: 'Video & Content',
    items: [
      {
        id: 'music-video-studio',
        title: 'Music Video Studio',
        icon: <VideoIcon className="h-4 w-4" />,
        href: '/music-video-studio',
      },
      {
        id: 'video-editor',
        title: 'Video Editor',
        icon: <CameraIcon className="h-4 w-4" />,
        href: '/video-editor',
      },
    ],
  },
  {
    id: 'analytics',
    title: 'Analytics',
    items: [
      {
        id: 'audience-analytics',
        title: 'Audience Analytics',
        icon: <PieChartIcon className="h-4 w-4" />,
        href: '/dashboard/audience-analytics',
      },
      {
        id: 'insights',
        title: 'Performance Insights',
        icon: <BarChartIcon className="h-4 w-4" />,
        href: '/dashboard/audience-analytics',
      },
    ],
  },
  {
    id: 'settings',
    title: 'Settings',
    items: [
      {
        id: 'preferences',
        title: 'Preferences',
        icon: <SettingsIcon className="h-4 w-4" />,
        href: '/dashboard/settings',
      },
    ],
  },
]; 