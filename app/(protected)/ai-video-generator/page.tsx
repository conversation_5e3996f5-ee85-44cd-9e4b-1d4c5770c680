'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  EnhancedAIVideoGenerator,
  AIVideoGenerator,
  SettingsPanel,
  ExportInterface,
  VideoEditingControls,
  ThumbnailGenerator
} from '@/components/ai-video-generator';
import {
  AIVideoGeneratorState,
  AIGenerationSettings,
  ExportSettings,
  VideoEditingState,
  GeneratedVideo,
  ThumbnailData
} from '@/components/ai-video-generator';
import {
  Wand2,
  Settings,
  Download,
  Edit,
  Image,
  Sparkles,
  Play,
  Upload,
  Monitor,
  Layers
} from 'lucide-react';
import { toast } from 'sonner';

export default function AIVideoGeneratorPage() {
  const [activeTab, setActiveTab] = useState('generator');
  const [selectedVideo, setSelectedVideo] = useState<GeneratedVideo | null>(null);
  
  // AI Video Generator State
  const [generatorState, setGeneratorState] = useState<AIVideoGeneratorState>({
    currentStep: 'upload',
    uploadedFiles: [],
    prompt: {
      text: '',
      style: {
        id: 'realistic',
        name: 'Realistic',
        description: 'Photorealistic video generation',
        thumbnail: '/styles/realistic.jpg',
        category: 'realistic',
        parameters: { style_strength: 0.8 }
      },
      mood: '',
      duration: 5,
      aspectRatio: '16:9',
      tags: [],
      negativePrompt: '',
      seed: undefined,
      strength: 0.8
    },
    generationSettings: {
      model: 'runway',
      resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
      fps: 30,
      duration: 5,
      quality: 'high',
      iterations: 1,
      guidanceScale: 7.5,
      seed: undefined
    },
    generatedVideos: [],
    processingQueue: [],
    exportSettings: {
      format: 'mp4',
      resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
      fps: 30,
      bitrate: 5000000,
      codec: 'h264',
      quality: 'high',
      includeAudio: true,
      watermark: {
        enabled: false,
        position: 'bottom-right',
        opacity: 0.7
      }
    }
  });

  // Video Editing State
  const [editingState, setEditingState] = useState<VideoEditingState>({
    selectedClip: undefined,
    trimStart: undefined,
    trimEnd: undefined,
    cropArea: undefined,
    filters: [],
    transformations: []
  });

  // Thumbnails State
  const [thumbnails, setThumbnails] = useState<ThumbnailData[]>([]);
  const [selectedThumbnailId, setSelectedThumbnailId] = useState<string>();

  // Handlers
  const handleVideoGenerated = useCallback((video: GeneratedVideo) => {
    setGeneratorState(prev => ({
      ...prev,
      generatedVideos: [...prev.generatedVideos, video]
    }));
    setSelectedVideo(video);
    toast.success('Video generated successfully!');
  }, []);

  const handleVideoExported = useCallback((videoUrl: string) => {
    toast.success('Video exported successfully!');
    // Handle video export logic here
  }, []);

  const handleGenerationSettingsChange = useCallback((settings: AIGenerationSettings) => {
    setGeneratorState(prev => ({
      ...prev,
      generationSettings: settings
    }));
  }, []);

  const handleExportSettingsChange = useCallback((settings: ExportSettings) => {
    setGeneratorState(prev => ({
      ...prev,
      exportSettings: settings
    }));
  }, []);

  const handleExport = useCallback(async (videoIds: string[], settings: ExportSettings) => {
    // Implement export logic here
    console.log('Exporting videos:', videoIds, 'with settings:', settings);
    return Promise.resolve();
  }, []);

  const handleEditingStateChange = useCallback((state: VideoEditingState) => {
    setEditingState(state);
  }, []);

  const handleApplyChanges = useCallback(() => {
    if (selectedVideo) {
      toast.success('Changes applied to video');
      // Apply editing changes to the selected video
    }
  }, [selectedVideo]);

  const handleResetChanges = useCallback(() => {
    setEditingState({
      selectedClip: undefined,
      trimStart: undefined,
      trimEnd: undefined,
      cropArea: undefined,
      filters: [],
      transformations: []
    });
    toast.info('Changes reset');
  }, []);

  const handleThumbnailsGenerated = useCallback((newThumbnails: ThumbnailData[]) => {
    setThumbnails(newThumbnails);
  }, []);

  const handleThumbnailSelected = useCallback((thumbnailId: string) => {
    setSelectedThumbnailId(thumbnailId);
    setThumbnails(prev => prev.map(thumb => ({
      ...thumb,
      isSelected: thumb.id === thumbnailId
    })));
  }, []);

  const handleThumbnailDeleted = useCallback((thumbnailId: string) => {
    setThumbnails(prev => prev.filter(thumb => thumb.id !== thumbnailId));
    if (selectedThumbnailId === thumbnailId) {
      setSelectedThumbnailId(undefined);
    }
  }, [selectedThumbnailId]);

  const handleCustomThumbnailUploaded = useCallback((thumbnail: ThumbnailData) => {
    setThumbnails(prev => [...prev, thumbnail]);
  }, []);

  const hasGeneratedVideos = generatorState.generatedVideos.length > 0;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-primary/10 rounded-full">
            <Wand2 className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-4xl font-bold">AI Video Generator</h1>
            <p className="text-xl text-muted-foreground">
              Create stunning videos with artificial intelligence
            </p>
          </div>
        </div>
        
        <div className="flex items-center justify-center gap-4">
          <Badge variant="secondary" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Powered by AI
          </Badge>
          <Badge variant="outline" className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            {generatorState.generatedVideos.length} Videos Generated
          </Badge>
        </div>
      </div>

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="generator" className="flex items-center gap-2">
            <Wand2 className="h-4 w-4" />
            Generator
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="editing" className="flex items-center gap-2" disabled={!selectedVideo}>
            <Edit className="h-4 w-4" />
            Editing
          </TabsTrigger>
          <TabsTrigger value="thumbnails" className="flex items-center gap-2" disabled={!selectedVideo}>
            <Image className="h-4 w-4" />
            Thumbnails
          </TabsTrigger>
          <TabsTrigger value="export" className="flex items-center gap-2" disabled={!hasGeneratedVideos}>
            <Download className="h-4 w-4" />
            Export
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generator" className="space-y-6">
          <AIVideoGenerator
            onVideoGenerated={handleVideoGenerated}
            onVideoExported={handleVideoExported}
          />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <SettingsPanel
            generationSettings={generatorState.generationSettings}
            exportSettings={generatorState.exportSettings}
            onGenerationSettingsChange={handleGenerationSettingsChange}
            onExportSettingsChange={handleExportSettingsChange}
            onSavePreset={(name, preset) => {
              toast.success(`Preset "${name}" saved`);
              // Save preset logic here
            }}
            onLoadPreset={(preset) => {
              toast.success('Preset loaded');
              // Load preset logic here
            }}
          />
        </TabsContent>

        <TabsContent value="editing" className="space-y-6">
          {selectedVideo ? (
            <VideoEditingControls
              video={selectedVideo}
              editingState={editingState}
              onEditingStateChange={handleEditingStateChange}
              onApplyChanges={handleApplyChanges}
              onResetChanges={handleResetChanges}
            />
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Edit className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="font-medium mb-2">No video selected</h3>
                <p className="text-sm text-muted-foreground">
                  Generate a video first to access editing tools
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="thumbnails" className="space-y-6">
          {selectedVideo ? (
            <ThumbnailGenerator
              video={selectedVideo}
              thumbnails={thumbnails}
              selectedThumbnailId={selectedThumbnailId}
              onThumbnailsGenerated={handleThumbnailsGenerated}
              onThumbnailSelected={handleThumbnailSelected}
              onThumbnailDeleted={handleThumbnailDeleted}
              onCustomThumbnailUploaded={handleCustomThumbnailUploaded}
            />
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Image className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="font-medium mb-2">No video selected</h3>
                <p className="text-sm text-muted-foreground">
                  Generate a video first to create thumbnails
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="export" className="space-y-6">
          {hasGeneratedVideos ? (
            <ExportInterface
              videos={generatorState.generatedVideos}
              exportSettings={generatorState.exportSettings}
              onExportSettingsChange={handleExportSettingsChange}
              onExport={handleExport}
              onDownload={(url, filename) => {
                // Create download link
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            />
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Download className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="font-medium mb-2">No videos to export</h3>
                <p className="text-sm text-muted-foreground">
                  Generate some videos first to access export options
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      {hasGeneratedVideos && (
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setActiveTab('editing')}
                disabled={!selectedVideo}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Selected Video
              </Button>
              <Button
                variant="outline"
                onClick={() => setActiveTab('thumbnails')}
                disabled={!selectedVideo}
              >
                <Image className="h-4 w-4 mr-2" />
                Generate Thumbnails
              </Button>
              <Button
                onClick={() => setActiveTab('export')}
              >
                <Download className="h-4 w-4 mr-2" />
                Export Videos
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Video Selection */}
      {hasGeneratedVideos && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Videos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {generatorState.generatedVideos.map((video) => (
                <Card 
                  key={video.id} 
                  className={`cursor-pointer transition-all ${
                    selectedVideo?.id === video.id ? 'ring-2 ring-primary' : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedVideo(video)}
                >
                  <CardContent className="p-4">
                    <div className="aspect-video bg-muted rounded mb-3 flex items-center justify-center">
                      {video.thumbnailUrl ? (
                        <img 
                          src={video.thumbnailUrl} 
                          alt={video.prompt}
                          className="w-full h-full object-cover rounded"
                        />
                      ) : (
                        <Play className="h-8 w-8 text-muted-foreground" />
                      )}
                    </div>
                    
                    <h4 className="font-medium mb-1 line-clamp-2">
                      {video.prompt.slice(0, 60)}...
                    </h4>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>{video.style.name}</span>
                      <span>{video.duration}s</span>
                    </div>
                    
                    {selectedVideo?.id === video.id && (
                      <Badge className="mt-2 w-full justify-center">
                        Selected
                      </Badge>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
