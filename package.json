{"name": "tune-base", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed:analytics": "ts-node prisma/seed-analytics.ts"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@auth/prisma-adapter": "^2.8.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@elevenlabs/elevenlabs-js": "^2.5.0", "@hookform/resolvers": "^4.1.3", "@next-auth/prisma-adapter": "^1.0.7", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tabler/icons-react": "^3.31.0", "@tanstack/react-table": "^8.21.2", "ai": "^4.3.16", "appwrite": "^17.0.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "elevenlabs": "^0.8.2", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.6.2", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.486.0", "moment": "^2.30.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.12.0", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@prisma/client": "^6.5.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "prisma": "^6.5.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}