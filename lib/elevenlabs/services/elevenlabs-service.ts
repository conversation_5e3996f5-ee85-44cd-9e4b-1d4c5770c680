// ElevenLabs Core Service
// Main service class for ElevenLabs API interactions

import { ELEVENLABS_API_BASE_URL, ELEVENLABS_MODELS, ELEVENLABS_OUTPUT_FORMATS } from '../constants';
import { extractErrorMessage, validateApiKey, validateTTSRequest } from '../utils';
import type { 
  ElevenLabsVoice, 
  ElevenLabsModel, 
  TTSRequest, 
  TTSResponse, 
  VoiceCloneRequest, 
  VoiceDesignRequest,
  VoiceLibraryResponse
} from '../types';

export class ElevenLabsService {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = ELEVENLABS_API_BASE_URL) {
    if (!validateApiKey(apiKey)) {
      throw new Error('Invalid API key format');
    }
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  /**
   * Create headers for API requests
   */
  private createHeaders(contentType: string = 'application/json'): Record<string, string> {
    return {
      'xi-api-key': this.apiKey,
      'Content-Type': contentType,
      'User-Agent': 'TuneBase/1.0',
    };
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage: string;
      
      try {
        const errorData = JSON.parse(errorText);
        errorMessage = extractErrorMessage({ response: { data: errorData } });
      } catch {
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }
      
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      return response.json();
    } else {
      return response.arrayBuffer() as T;
    }
  }

  /**
   * Get available voices
   */
  async getVoices(): Promise<ElevenLabsVoice[]> {
    const response = await fetch(`${this.baseUrl}/voices`, {
      headers: this.createHeaders(),
    });

    const data = await this.handleResponse<VoiceLibraryResponse>(response);
    return data.voices;
  }

  /**
   * Get voice by ID
   */
  async getVoice(voiceId: string): Promise<ElevenLabsVoice> {
    const response = await fetch(`${this.baseUrl}/voices/${voiceId}`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<ElevenLabsVoice>(response);
  }

  /**
   * Get available models
   */
  async getModels(): Promise<ElevenLabsModel[]> {
    const response = await fetch(`${this.baseUrl}/models`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<ElevenLabsModel[]>(response);
  }

  /**
   * Generate speech from text
   */
  async textToSpeech(request: TTSRequest): Promise<TTSResponse> {
    const validationErrors = validateTTSRequest(request);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    const body = {
      text: request.text,
      model_id: request.model_id || ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2,
      voice_settings: request.voice_settings,
      pronunciation_dictionary_locators: request.pronunciation_dictionary_locators,
      seed: request.seed,
      previous_text: request.previous_text,
      next_text: request.next_text,
      previous_request_ids: request.previous_request_ids,
      next_request_ids: request.next_request_ids,
      output_format: request.output_format || ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128,
      apply_text_normalization: request.apply_text_normalization || 'auto',
    };

    const response = await fetch(`${this.baseUrl}/text-to-speech/${request.voice_id}`, {
      method: 'POST',
      headers: this.createHeaders(),
      body: JSON.stringify(body),
    });

    const audio = await this.handleResponse<ArrayBuffer>(response);
    
    return {
      audio,
      // TODO: Add alignment support when available
      alignment: undefined,
      normalized_alignment: undefined,
    };
  }

  /**
   * Generate speech with timestamps
   */
  async textToSpeechWithTimestamps(request: TTSRequest): Promise<TTSResponse> {
    const validationErrors = validateTTSRequest(request);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    const body = {
      text: request.text,
      model_id: request.model_id || ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2,
      voice_settings: request.voice_settings,
      pronunciation_dictionary_locators: request.pronunciation_dictionary_locators,
      seed: request.seed,
      previous_text: request.previous_text,
      next_text: request.next_text,
      previous_request_ids: request.previous_request_ids,
      next_request_ids: request.next_request_ids,
      output_format: request.output_format || ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128,
      apply_text_normalization: request.apply_text_normalization || 'auto',
    };

    const response = await fetch(`${this.baseUrl}/text-to-speech/${request.voice_id}/with-timestamps`, {
      method: 'POST',
      headers: this.createHeaders(),
      body: JSON.stringify(body),
    });

    const data = await this.handleResponse<{
      audio_base64: string;
      alignment: TTSResponse['alignment'];
      normalized_alignment: TTSResponse['normalized_alignment'];
    }>(response);

    // Convert base64 to ArrayBuffer
    const binaryString = atob(data.audio_base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    return {
      audio: bytes.buffer,
      alignment: data.alignment,
      normalized_alignment: data.normalized_alignment,
    };
  }

  /**
   * Clone a voice
   */
  async cloneVoice(request: VoiceCloneRequest): Promise<ElevenLabsVoice> {
    const formData = new FormData();
    formData.append('name', request.name);
    
    if (request.description) {
      formData.append('description', request.description);
    }
    
    if (request.labels) {
      formData.append('labels', JSON.stringify(request.labels));
    }
    
    if (request.remove_background_noise !== undefined) {
      formData.append('remove_background_noise', request.remove_background_noise.toString());
    }

    // Add files
    request.files.forEach((file, index) => {
      formData.append(`files`, file);
    });

    const response = await fetch(`${this.baseUrl}/voices/add`, {
      method: 'POST',
      headers: {
        'xi-api-key': this.apiKey,
        'User-Agent': 'TuneBase/1.0',
      },
      body: formData,
    });

    return this.handleResponse<ElevenLabsVoice>(response);
  }

  /**
   * Create instant voice clone
   */
  async createInstantVoiceClone(request: VoiceCloneRequest): Promise<ElevenLabsVoice> {
    const formData = new FormData();
    formData.append('name', request.name);
    
    if (request.description) {
      formData.append('description', request.description);
    }
    
    if (request.labels) {
      formData.append('labels', JSON.stringify(request.labels));
    }

    // Add files
    request.files.forEach((file, index) => {
      formData.append(`files`, file);
    });

    const response = await fetch(`${this.baseUrl}/voices/ivc/add`, {
      method: 'POST',
      headers: {
        'xi-api-key': this.apiKey,
        'User-Agent': 'TuneBase/1.0',
      },
      body: formData,
    });

    return this.handleResponse<ElevenLabsVoice>(response);
  }

  /**
   * Design a voice
   */
  async designVoice(request: VoiceDesignRequest): Promise<ElevenLabsVoice> {
    const body = {
      name: request.name,
      text: request.text,
      voice_description: request.voice_description,
      generated_voice_id: request.generated_voice_id,
    };

    const response = await fetch(`${this.baseUrl}/voices/design`, {
      method: 'POST',
      headers: this.createHeaders(),
      body: JSON.stringify(body),
    });

    return this.handleResponse<ElevenLabsVoice>(response);
  }

  /**
   * Delete a voice
   */
  async deleteVoice(voiceId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/voices/${voiceId}`, {
      method: 'DELETE',
      headers: this.createHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to delete voice: ${response.statusText}`);
    }
  }

  /**
   * Edit voice settings
   */
  async editVoice(
    voiceId: string, 
    updates: { name?: string; description?: string; labels?: Record<string, string> }
  ): Promise<ElevenLabsVoice> {
    const response = await fetch(`${this.baseUrl}/voices/${voiceId}/edit`, {
      method: 'POST',
      headers: this.createHeaders(),
      body: JSON.stringify(updates),
    });

    return this.handleResponse<ElevenLabsVoice>(response);
  }

  /**
   * Get voice settings
   */
  async getVoiceSettings(voiceId: string): Promise<ElevenLabsVoice['settings']> {
    const response = await fetch(`${this.baseUrl}/voices/${voiceId}/settings`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<ElevenLabsVoice['settings']>(response);
  }

  /**
   * Update voice settings
   */
  async updateVoiceSettings(
    voiceId: string, 
    settings: Partial<ElevenLabsVoice['settings']>
  ): Promise<ElevenLabsVoice['settings']> {
    const response = await fetch(`${this.baseUrl}/voices/${voiceId}/settings/edit`, {
      method: 'POST',
      headers: this.createHeaders(),
      body: JSON.stringify(settings),
    });

    return this.handleResponse<ElevenLabsVoice['settings']>(response);
  }

  /**
   * Get voice library
   */
  async getVoiceLibrary(page: number = 1, pageSize: number = 30): Promise<{
    voices: ElevenLabsVoice[];
    has_more: boolean;
    last_sort_id: string;
  }> {
    const params = new URLSearchParams({
      page_size: pageSize.toString(),
      page: page.toString(),
    });

    const response = await fetch(`${this.baseUrl}/voices/library?${params}`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<{
      voices: ElevenLabsVoice[];
      has_more: boolean;
      last_sort_id: string;
    }>(response);
  }

  /**
   * Add voice from library
   */
  async addVoiceFromLibrary(publicUserId: string, voiceId: string): Promise<ElevenLabsVoice> {
    const response = await fetch(`${this.baseUrl}/voices/library/${publicUserId}/${voiceId}/add`, {
      method: 'POST',
      headers: this.createHeaders(),
    });

    return this.handleResponse<ElevenLabsVoice>(response);
  }

  /**
   * Get pronunciation dictionaries
   */
  async getPronunciationDictionaries(): Promise<any[]> {
    const response = await fetch(`${this.baseUrl}/pronunciation-dictionaries`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<any[]>(response);
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ status: string }> {
    const response = await fetch(`${this.baseUrl}/health`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<{ status: string }>(response);
  }
}