// ElevenLabs Constants
// API constants and configuration

export const E<PERSON>VENLABS_API_BASE_URL = 'https://api.elevenlabs.io/v1';

export const ELEVENLABS_MODELS = {
  ELEVEN_MULTILINGUAL_V2: 'eleven_multilingual_v2',
  ELEVEN_TURBO_V2_5: 'eleven_turbo_v2_5',
  ELEVEN_FLASH_V2_5: 'eleven_flash_v2_5',
  ELEVEN_V3_ALPHA: 'eleven_v3_alpha',
} as const;

export const ELEVENLABS_OUTPUT_FORMATS = {
  MP3_22050_32: 'mp3_22050_32',
  MP3_44100_32: 'mp3_44100_32',
  MP3_44100_64: 'mp3_44100_64',
  MP3_44100_96: 'mp3_44100_96',
  MP3_44100_128: 'mp3_44100_128',
  MP3_44100_192: 'mp3_44100_192',
  PCM_16000: 'pcm_16000',
  PCM_22050: 'pcm_22050',
  PCM_24000: 'pcm_24000',
  PCM_44100: 'pcm_44100',
  ULAW_8000: 'ulaw_8000',
} as const;

export const VOICE_CATEGORIES = {
  PREMADE: 'premade',
  CLONED: 'cloned',
  GENERATED: 'generated',
  PROFESSIONAL: 'professional',
} as const;

export const VOICE_SETTINGS_DEFAULTS = {
  STABILITY: 0.5,
  SIMILARITY_BOOST: 0.5,
  STYLE: 0.0,
  USE_SPEAKER_BOOST: true,
} as const;

export const SUPPORTED_LANGUAGES = [
  'en', 'ja', 'zh', 'de', 'hi', 'fr', 'ko', 'pt', 'it', 'es', 'id', 'nl', 'tr', 'ar', 'sv', 'cs', 'ru', 'da', 'el', 'fi', 'hr', 'ms', 'sk', 'ta', 'th', 'uk', 'vi', 'no', 'he', 'hu', 'bg', 'ca', 'sl'
] as const;

export const TEXT_NORMALIZATION_OPTIONS = {
  AUTO: 'auto',
  ON: 'on',
  OFF: 'off',
} as const;

export const SUBSCRIPTION_TIERS = {
  FREE: 'free',
  STARTER: 'starter',
  CREATOR: 'creator',
  PRO: 'pro',
  SCALE: 'scale',
  BUSINESS: 'business',
} as const;

export const VOICE_VERIFICATION_STATES = {
  VERIFIED: 'verified',
  REQUIRES_VERIFICATION: 'requires_verification',
  VERIFICATION_FAILED: 'verification_failed',
} as const;

export const GENERATION_STATES = {
  IDLE: 'idle',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export const STREAMING_CHUNK_SCHEDULES = {
  REAL_TIME: [50],
  BALANCED: [120, 160, 250, 290],
  QUALITY: [500],
} as const;

export const ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  QUOTA_EXCEEDED: 429,
  VALIDATION_ERROR: 422,
  SERVER_ERROR: 500,
} as const;

export const MAX_TEXT_LENGTH = {
  [ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2]: 2500,
  [ELEVENLABS_MODELS.ELEVEN_TURBO_V2_5]: 2500,
  [ELEVENLABS_MODELS.ELEVEN_FLASH_V2_5]: 2500,
  [ELEVENLABS_MODELS.ELEVEN_V3_ALPHA]: 2500,
} as const;

export const VOICE_CLONE_REQUIREMENTS = {
  MIN_AUDIO_DURATION: 60, // seconds
  MAX_AUDIO_DURATION: 300, // seconds
  MIN_FILES: 1,
  MAX_FILES: 25,
  SUPPORTED_FORMATS: ['mp3', 'wav', 'flac', 'm4a', 'ogg'],
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB per file
} as const;

export const VOICE_DESIGN_LIMITS = {
  MIN_DESCRIPTION_LENGTH: 10,
  MAX_DESCRIPTION_LENGTH: 500,
  MIN_TEXT_LENGTH: 50,
  MAX_TEXT_LENGTH: 1000,
} as const;