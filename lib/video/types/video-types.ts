// Core Video Types and Interfaces

export interface VideoClip {
  id: string;
  type: 'video' | 'audio' | 'image' | 'text' | 'ai-generated';
  source: string | Blob | File;
  startTime: number;
  endTime: number;
  duration: number;
  track: number;
  position: number;
  metadata?: VideoMetadata;
  effects?: Effect[];
  transitions?: Transition[];
}

export interface VideoMetadata {
  width: number;
  height: number;
  fps: number;
  bitrate?: number;
  codec?: string;
  format?: string;
  fileSize?: number;
  createdAt?: Date;
  modifiedAt?: Date;
}

export interface Timeline {
  id: string;
  name: string;
  duration: number;
  fps: number;
  resolution: VideoResolution;
  tracks: Track[];
  clips: VideoClip[];
  createdAt: Date;
  modifiedAt: Date;
}

export interface Track {
  id: string;
  type: 'video' | 'audio' | 'subtitle';
  name: string;
  index: number;
  enabled: boolean;
  locked: boolean;
  volume?: number;
  opacity?: number;
}

export interface VideoResolution {
  width: number;
  height: number;
  aspectRatio: string;
}

export interface Effect {
  id: string;
  type: EffectType;
  name: string;
  parameters: Record<string, any>;
  startTime: number;
  endTime: number;
  enabled: boolean;
}

export type EffectType = 
  | 'blur'
  | 'brightness'
  | 'contrast'
  | 'saturation'
  | 'hue'
  | 'sepia'
  | 'grayscale'
  | 'invert'
  | 'opacity'
  | 'scale'
  | 'rotate'
  | 'translate'
  | 'crop'
  | 'fade'
  | 'zoom'
  | 'stabilization'
  | 'noise-reduction'
  | 'color-correction'
  | 'chroma-key';

export interface Transition {
  id: string;
  type: TransitionType;
  duration: number;
  parameters: Record<string, any>;
  easing: EasingFunction;
}

export type TransitionType =
  | 'fade'
  | 'dissolve'
  | 'wipe'
  | 'slide'
  | 'push'
  | 'iris'
  | 'zoom'
  | 'rotate'
  | 'flip'
  | 'cube'
  | 'page-turn';

export type EasingFunction = 
  | 'linear'
  | 'ease-in'
  | 'ease-out'
  | 'ease-in-out'
  | 'cubic-bezier';

export interface RenderSettings {
  resolution: VideoResolution;
  fps: number;
  bitrate: number;
  codec: VideoCodec;
  format: VideoFormat;
  quality: 'low' | 'medium' | 'high' | 'ultra';
  hardwareAcceleration: boolean;
}

export type VideoCodec = 'h264' | 'h265' | 'vp8' | 'vp9' | 'av1';
export type VideoFormat = 'mp4' | 'webm' | 'mov' | 'avi' | 'mkv';

export interface AIVideoGenerationRequest {
  prompt: string;
  style?: 'realistic' | 'animated' | 'cinematic' | 'artistic';
  duration: number;
  resolution: VideoResolution;
  fps: number;
  seed?: number;
  model?: 'runway' | 'pika' | 'stability' | 'custom';
}

export interface AIVideoGenerationResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  videoUrl?: string;
  thumbnailUrl?: string;
  duration?: number;
  metadata?: VideoMetadata;
  error?: string;
  progress?: number;
}

export interface TTSRequest {
  text: string;
  voice: string;
  language: string;
  speed: number;
  pitch: number;
  volume: number;
  provider: 'elevenlabs' | 'openai' | 'google' | 'azure';
  model?: string;
}

export interface TTSResponse {
  id: string;
  audioUrl: string;
  duration: number;
  format: 'mp3' | 'wav' | 'ogg';
  metadata: {
    sampleRate: number;
    bitrate: number;
    channels: number;
  };
}

export interface VideoEditorState {
  timeline: Timeline;
  selectedClips: string[];
  currentTime: number;
  isPlaying: boolean;
  zoom: number;
  snapToGrid: boolean;
  renderSettings: RenderSettings;
  history: EditorAction[];
  historyIndex: number;
}

export interface EditorAction {
  id: string;
  type: string;
  timestamp: Date;
  data: any;
  description: string;
}

export interface VideoSystemConfig {
  ffmpegPath?: string;
  tempDirectory?: string;
  maxFileSize?: number;
  supportedFormats?: string[];
  aiProviders?: {
    runway?: { apiKey: string };
    pika?: { apiKey: string };
    stability?: { apiKey: string };
    elevenlabs?: { apiKey: string };
    openai?: { apiKey: string };
  };
  webcodecs?: {
    enabled: boolean;
    hardwareAcceleration: boolean;
  };
}

export interface VideoProcessingJob {
  id: string;
  type: 'render' | 'export' | 'ai-generation' | 'tts';
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  startTime: Date;
  endTime?: Date;
  input: any;
  output?: any;
  error?: string;
}

// Event Types
export interface VideoSystemEvents {
  'clip:added': { clip: VideoClip };
  'clip:removed': { clipId: string };
  'clip:updated': { clip: VideoClip };
  'timeline:updated': { timeline: Timeline };
  'playback:started': { currentTime: number };
  'playback:paused': { currentTime: number };
  'playback:stopped': { currentTime: number };
  'playback:timeupdate': { currentTime: number };
  'render:started': { jobId: string };
  'render:progress': { jobId: string; progress: number };
  'render:completed': { jobId: string; output: string };
  'render:failed': { jobId: string; error: string };
  'ai:generation:started': { requestId: string };
  'ai:generation:progress': { requestId: string; progress: number };
  'ai:generation:completed': { requestId: string; result: AIVideoGenerationResponse };
  'ai:generation:failed': { requestId: string; error: string };
}

// Utility Types
export type VideoSystemEventType = keyof VideoSystemEvents;
export type VideoSystemEventHandler<T extends VideoSystemEventType> = (data: VideoSystemEvents[T]) => void;

export interface VideoAnalytics {
  duration: number;
  resolution: VideoResolution;
  fps: number;
  bitrate: number;
  fileSize: number;
  codec: string;
  format: string;
  hasAudio: boolean;
  audioChannels?: number;
  audioSampleRate?: number;
  audioBitrate?: number;
  thumbnails: string[];
  waveform?: number[];
}